import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SkipLink from "@/components/ui/SkipLink";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Coding with Prasum - Learn Modern Web Development",
  description: "Join the coding revolution with Prasum! Learn modern web development, programming fundamentals, and industry best practices through comprehensive tutorials and hands-on projects.",
  keywords: "web development, programming, tutorials, React, Next.js, JavaScript, TypeScript, coding",
  authors: [{ name: "Prasum" }],
  creator: "Prasum",
  openGraph: {
    title: "Coding with Prasum - Learn Modern Web Development",
    description: "Join the coding revolution with <PERSON>rasum! Learn modern web development through comprehensive tutorials and hands-on projects.",
    url: "https://codingwithprasum.com",
    siteName: "Coding with Prasum",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Coding with Prasum - Learn Modern Web Development",
    description: "Join the coding revolution with Prasum! Learn modern web development through comprehensive tutorials and hands-on projects.",
    creator: "@codingwithprasum",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" role="main">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
