exports.id=195,exports.ids=[195],exports.modules={386:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,8100,23)),Promise.resolve().then(c.bind(c,9836))},1181:()=>{},1381:()=>{},1818:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3889,23)),Promise.resolve().then(c.t.bind(c,3592,23)),Promise.resolve().then(c.t.bind(c,8998,23)),Promise.resolve().then(c.t.bind(c,3193,23)),Promise.resolve().then(c.t.bind(c,61,23)),Promise.resolve().then(c.t.bind(c,738,23)),Promise.resolve().then(c.t.bind(c,9837,23)),Promise.resolve().then(c.t.bind(c,5559,23)),Promise.resolve().then(c.bind(c,5925))},2434:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,7711,23)),Promise.resolve().then(c.t.bind(c,6510,23)),Promise.resolve().then(c.t.bind(c,4056,23)),Promise.resolve().then(c.t.bind(c,4191,23)),Promise.resolve().then(c.t.bind(c,8599,23)),Promise.resolve().then(c.t.bind(c,4943,23)),Promise.resolve().then(c.t.bind(c,2591,23)),Promise.resolve().then(c.t.bind(c,1305,23)),Promise.resolve().then(c.t.bind(c,9855,23))},3215:(a,b,c)=>{"use strict";c.d(b,{Ln:()=>d,PH:()=>e,lj:()=>f});let d=[{id:"1",title:"Project Alpha",description:"Advanced web application with modern architecture",image:"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",technologies:["React","Next.js","TypeScript"]},{id:"2",title:"Project Beta",description:"Mobile-first responsive design system",image:"https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",technologies:["Vue.js","Tailwind CSS","Node.js"]},{id:"3",title:"Project Gamma",description:"Full-stack e-commerce platform",image:"https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",technologies:["React","Express","MongoDB"]},{id:"4",title:"John Doe Framework",description:"Custom JavaScript framework for rapid development",image:"https://images.unsplash.com/photo-1542831371-29b0f74f9713?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",technologies:["JavaScript","Webpack","Babel"]},{id:"5",title:"LastLine Code Together",description:"Collaborative coding platform with real-time features",image:"https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",technologies:["Socket.io","React","Redis"]}],e=[{name:"Basic",price:"FREE",features:["Access to tutorials","Basic community access","5 project templates"],ctaText:"Get Started"},{name:"Pro",price:"$50",features:["Advanced tutorials","Premium community","Unlimited projects","1-on-1 mentorship"],isPopular:!0,ctaText:"Upgrade Now"}],f=[{name:"YouTube",href:"https://youtube.com/@codingwithprasum",icon:"youtube"},{name:"GitHub",href:"https://github.com/codingwithprasum",icon:"github"},{name:"Twitter",href:"https://twitter.com/codingwithprasum",icon:"twitter"},{name:"LinkedIn",href:"https://linkedin.com/in/codingwithprasum",icon:"linkedin"}]},4681:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1411).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CWP\\\\coding-with-prasum\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\CWP\\coding-with-prasum\\src\\components\\Header.tsx","default")},5301:()=>{},6221:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(339);function e(){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})})}},7338:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,810,23)),Promise.resolve().then(c.bind(c,4681))},7385:(a,b,c)=>{"use strict";c.d(b,{Cn:()=>d,Zt:()=>e});let d=[{name:"Home",href:"/"},{name:"Projects",href:"/projects"},{name:"About",href:"/about"},{name:"Articles",href:"/articles"}],e=["React","JavaScript","TypeScript","Node.js","Python","Java","C++","Go","Rust","PHP","Ruby","Swift","Kotlin","Vue.js","Angular","Svelte","Next.js","Nuxt.js","Express","Django","Spring","Laravel","Rails","Flutter","React Native"]},8412:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r,metadata:()=>q});var d=c(339),e=c(2958),f=c.n(e),g=c(8848),h=c.n(g);c(1181);var i=c(4681),j=c(810),k=c.n(j),l=c(3215);let m=[{title:"Quick Links",links:[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Projects",href:"/projects"},{name:"Articles",href:"/articles"}]},{title:"Resources",links:[{name:"Tutorials",href:"/tutorials"},{name:"Courses",href:"/courses"},{name:"Blog",href:"/blog"},{name:"Newsletter",href:"/newsletter"}]},{title:"Community",links:[{name:"Discord",href:"/discord"},{name:"GitHub",href:"https://github.com/codingwithprasum"},{name:"YouTube",href:"https://youtube.com/@codingwithprasum"},{name:"Twitter",href:"https://twitter.com/codingwithprasum"}]},{title:"Support",links:[{name:"Contact",href:"/contact"},{name:"FAQ",href:"/faq"},{name:"Help Center",href:"/help"},{name:"Privacy Policy",href:"/privacy"}]}];function n({name:a}){return({youtube:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})}),github:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})}),twitter:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})}),linkedin:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})})[a.toLowerCase()]||null}function o(){let a=new Date().getFullYear();return(0,d.jsx)("footer",{className:"bg-card border-t border-border",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-1",children:[(0,d.jsx)(k(),{href:"/",className:"flex items-center space-x-2 mb-4",children:(0,d.jsx)("span",{className:"text-xl font-bold text-card-foreground",children:"Coding with Prasum"})}),(0,d.jsx)("p",{className:"text-muted-foreground text-sm mb-6",children:"Empowering developers with modern web development tutorials, projects, and community support."}),(0,d.jsx)("div",{className:"flex space-x-4",children:l.lj.map(a=>(0,d.jsx)("a",{href:a.href,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors p-2 hover:bg-accent rounded-md","aria-label":`Follow on ${a.name}`,children:(0,d.jsx)(n,{name:a.icon})},a.name))})]}),m.map(a=>(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-sm font-semibold text-card-foreground uppercase tracking-wider mb-4",children:a.title}),(0,d.jsx)("ul",{className:"space-y-3",children:a.links.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(k(),{href:a.href,className:"text-muted-foreground hover:text-card-foreground transition-colors text-sm",children:a.name})},a.name))})]},a.title))]}),(0,d.jsx)("div",{className:"border-t border-border pt-8 mb-8",children:(0,d.jsxs)("div",{className:"max-w-md",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-card-foreground mb-2",children:"Stay Updated"}),(0,d.jsx)("p",{className:"text-muted-foreground text-sm mb-4",children:"Get the latest tutorials and coding tips delivered to your inbox."}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-3 py-2 bg-input border border-border rounded-md text-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"}),(0,d.jsx)("button",{className:"px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90 transition-colors",children:"Subscribe"})]})]})}),(0,d.jsxs)("div",{className:"border-t border-border pt-8 flex flex-col sm:flex-row justify-between items-center",children:[(0,d.jsxs)("p",{className:"text-muted-foreground text-sm",children:["\xa9 ",a," Coding with Prasum. All rights reserved."]}),(0,d.jsxs)("div",{className:"flex space-x-6 mt-4 sm:mt-0",children:[(0,d.jsx)(k(),{href:"/terms",className:"text-muted-foreground hover:text-card-foreground text-sm transition-colors",children:"Terms of Service"}),(0,d.jsx)(k(),{href:"/privacy",className:"text-muted-foreground hover:text-card-foreground text-sm transition-colors",children:"Privacy Policy"}),(0,d.jsx)(k(),{href:"/cookies",className:"text-muted-foreground hover:text-card-foreground text-sm transition-colors",children:"Cookie Policy"})]})]})]})})}function p(){return(0,d.jsx)("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm font-medium transition-all duration-200",children:"Skip to main content"})}let q={title:"Coding with Prasum - Learn Modern Web Development",description:"Join the coding revolution with Prasum! Learn modern web development, programming fundamentals, and industry best practices through comprehensive tutorials and hands-on projects.",keywords:"web development, programming, tutorials, React, Next.js, JavaScript, TypeScript, coding",authors:[{name:"Prasum"}],creator:"Prasum",openGraph:{title:"Coding with Prasum - Learn Modern Web Development",description:"Join the coding revolution with Prasum! Learn modern web development through comprehensive tutorials and hands-on projects.",url:"https://codingwithprasum.com",siteName:"Coding with Prasum",type:"website"},twitter:{card:"summary_large_image",title:"Coding with Prasum - Learn Modern Web Development",description:"Join the coding revolution with Prasum! Learn modern web development through comprehensive tutorials and hands-on projects.",creator:"@codingwithprasum"}};function r({children:a}){return(0,d.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,d.jsxs)("body",{className:`${f().variable} ${h().variable} antialiased min-h-screen flex flex-col`,children:[(0,d.jsx)(p,{}),(0,d.jsx)(i.default,{}),(0,d.jsx)("main",{id:"main-content",className:"flex-1",role:"main",children:a}),(0,d.jsx)(o,{})]})})}},9014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(5928);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},9836:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(2089),e=c(6444),f=c(8100),g=c.n(f),h=c(7385),i=c(4921),j=c(2498);function k(...a){return(0,j.QP)((0,i.$)(a))}function l(){let[a,b]=(0,e.useState)(!1);return(0,d.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",role:"banner",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,d.jsx)(g(),{href:"/",className:"flex items-center space-x-2","aria-label":"Coding with Prasum - Home",children:(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsx)("span",{className:"text-xl font-bold text-foreground",children:"Coding with Prasum"})})}),(0,d.jsx)("nav",{className:"hidden md:flex items-center space-x-8",role:"navigation","aria-label":"Main navigation",children:h.Cn.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:a.name},a.name))}),(0,d.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,d.jsx)(g(),{href:"/subscribe",className:"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2",children:"Subscribe"}),(0,d.jsx)(g(),{href:"/contact",className:"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2",children:"Contact"})]}),(0,d.jsxs)("button",{className:"md:hidden inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary",onClick:()=>b(!a),"aria-expanded":a,"aria-controls":"mobile-menu","aria-label":a?"Close main menu":"Open main menu",children:[(0,d.jsx)("span",{className:"sr-only",children:a?"Close main menu":"Open main menu"}),(0,d.jsx)("svg",{className:k("h-6 w-6",a?"hidden":"block"),fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"})}),(0,d.jsx)("svg",{className:k("h-6 w-6",a?"block":"hidden"),fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})})]})]}),a&&(0,d.jsx)("div",{className:"md:hidden",id:"mobile-menu",children:(0,d.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-border",role:"navigation","aria-label":"Mobile navigation",children:[h.Cn.map(a=>(0,d.jsx)(g(),{href:a.href,className:"block px-3 py-2 text-base font-medium text-muted-foreground hover:text-foreground hover:bg-accent rounded-md",onClick:()=>b(!1),children:a.name},a.name)),(0,d.jsxs)("div",{className:"flex flex-col space-y-2 px-3 pt-4",children:[(0,d.jsx)(g(),{href:"/subscribe",className:"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2",onClick:()=>b(!1),children:"Subscribe"}),(0,d.jsx)(g(),{href:"/contact",className:"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2",onClick:()=>b(!1),children:"Contact"})]})]})})]})})}}};