import { Project, LearningPlan, NavItem, SocialLink } from "@/types";

export const navItems: NavItem[] = [
  { name: "Home", href: "/" },
  { name: "Projects", href: "/projects" },
  { name: "About", href: "/about" },
  { name: "Articles", href: "/articles" },
];

export const featuredProjects: Project[] = [
  {
    id: "1",
    title: "Project Alpha",
    description: "Advanced web application with modern architecture",
    image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",
    technologies: ["React", "Next.js", "TypeScript"],
  },
  {
    id: "2",
    title: "Project Beta",
    description: "Mobile-first responsive design system",
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",
    technologies: ["Vue.js", "Tailwind CSS", "Node.js"],
  },
  {
    id: "3",
    title: "Project Gamma",
    description: "Full-stack e-commerce platform",
    image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",
    technologies: ["React", "Express", "MongoDB"],
  },
  {
    id: "4",
    title: "John <PERSON>e Framework",
    description: "Custom JavaScript framework for rapid development",
    image: "https://images.unsplash.com/photo-1542831371-29b0f74f9713?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",
    technologies: ["JavaScript", "Webpack", "Babel"],
  },
  {
    id: "5",
    title: "LastLine Code Together",
    description: "Collaborative coding platform with real-time features",
    image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop&crop=entropy&auto=format&q=80",
    technologies: ["Socket.io", "React", "Redis"],
  },
];

export const technologies = [
  "React", "JavaScript", "TypeScript", "Node.js", "Python", "Java",
  "C++", "Go", "Rust", "PHP", "Ruby", "Swift", "Kotlin", "Vue.js",
  "Angular", "Svelte", "Next.js", "Nuxt.js", "Express", "Django",
  "Spring", "Laravel", "Rails", "Flutter", "React Native"
];

export const learningPlans: LearningPlan[] = [
  {
    name: "Basic",
    price: "FREE",
    features: [
      "Access to tutorials",
      "Basic community access",
      "5 project templates"
    ],
    ctaText: "Get Started"
  },
  {
    name: "Pro",
    price: "$50",
    features: [
      "Advanced tutorials",
      "Premium community",
      "Unlimited projects",
      "1-on-1 mentorship"
    ],
    isPopular: true,
    ctaText: "Upgrade Now"
  }
];

export const socialLinks: SocialLink[] = [
  { name: "YouTube", href: "https://youtube.com/@codingwithprasum", icon: "youtube" },
  { name: "GitHub", href: "https://github.com/codingwithprasum", icon: "github" },
  { name: "Twitter", href: "https://twitter.com/codingwithprasum", icon: "twitter" },
  { name: "LinkedIn", href: "https://linkedin.com/in/codingwithprasum", icon: "linkedin" },
];
