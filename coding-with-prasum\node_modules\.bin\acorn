#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/acorn/bin/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/acorn/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/node_modules:/mnt/c/Users/<USER>/Desktop/node_modules:/mnt/c/Users/<USER>/node_modules:/mnt/c/Users/<USER>/mnt/c/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/acorn/bin/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/acorn/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/node_modules:/mnt/c/Users/<USER>/Desktop/node_modules:/mnt/c/Users/<USER>/node_modules:/mnt/c/Users/<USER>/mnt/c/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../acorn/bin/acorn" "$@"
else
  exec node  "$basedir/../acorn/bin/acorn" "$@"
fi
