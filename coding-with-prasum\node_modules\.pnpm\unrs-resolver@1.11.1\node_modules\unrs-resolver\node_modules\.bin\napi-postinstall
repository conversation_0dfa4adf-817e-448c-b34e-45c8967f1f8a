#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/napi-postinstall@0.3.2/node_modules/napi-postinstall/lib/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/napi-postinstall@0.3.2/node_modules/napi-postinstall/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/napi-postinstall@0.3.2/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/napi-postinstall@0.3.2/node_modules/napi-postinstall/lib/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/napi-postinstall@0.3.2/node_modules/napi-postinstall/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/napi-postinstall@0.3.2/node_modules:/mnt/c/Users/<USER>/Desktop/CWP/coding-with-prasum/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../napi-postinstall@0.3.2/node_modules/napi-postinstall/lib/cli.js" "$@"
else
  exec node  "$basedir/../../../../../napi-postinstall@0.3.2/node_modules/napi-postinstall/lib/cli.js" "$@"
fi
